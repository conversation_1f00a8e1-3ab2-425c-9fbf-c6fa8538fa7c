# Service Bus 优化方案说明

## 问题分析

根据您描述的"线上突然监听不到消息，导致serviceBus消息堵塞"问题，主要原因可能包括：

### 1. 连接管理问题
- 原配置禁用了连接缓存，缺乏重连机制
- 没有心跳检测和连接健康检查
- 网络波动或Service Bus服务异常时无法自动恢复

### 2. 监听器配置不完善
- 缺少并发配置，单线程处理可能成为瓶颈
- 没有超时配置，可能导致监听器假死
- 异常处理机制不完善

### 3. 缺乏监控和预警
- 没有消息堆积监控
- 没有连接状态监控
- 没有自动恢复机制

## 优化方案

### 1. 连接配置优化

**JmsConfig.java 优化要点：**
- 启用连接缓存和重连机制
- 增加连接超时、发送超时等配置
- 优化监听器容器配置，增加并发和恢复机制

### 2. 新增组件

#### ServiceBusHealthIndicator
- 提供健康检查功能
- 集成到Spring Boot Actuator
- 可通过 `/actuator/health` 端点查看状态

#### ServiceBusMonitor
- 定时监控消息队列状态
- 消息堆积告警
- 连接异常检测

#### ServiceBusRecoveryService
- 自动检测和恢复失效的监听器
- 支持手动触发恢复
- 连接异常时自动重启监听器

#### ServiceBusAlertService
- 统一告警服务
- 支持邮件、短信、Webhook等多种告警方式
- 异步发送，不影响主流程

#### ServiceBusManagementController
- 提供管理接口
- 监控指标查询
- 手动恢复操作

### 3. 监听器优化

**优化后的监听器特点：**
- 增加并发配置
- 完善异常处理和消息确认机制
- 增加处理时间统计
- 死信队列处理优化

## 使用方法

### 1. 配置更新

将 `servicebus-config-template.yml` 中的配置添加到您的配置文件中：

```yaml
spring:
  jms:
    listener:
      concurrency: "1-5"
      receive-timeout: 30000
      recovery-interval: 5000
      idle-timeout: 60000

messageQueue:
  monitor:
    enabled: true
    alert:
      threshold: 100
      interval: 300000
  recovery:
    enabled: true
    maxRetries: 3
```

### 2. 监控接口

**健康检查：**
```
GET /api/servicebus/health
```

**监控指标：**
```
GET /api/servicebus/metrics
```

**状态报告：**
```
GET /api/servicebus/status/report
```

**手动恢复：**
```
POST /api/servicebus/recovery/manual
```

### 3. 告警配置

在配置文件中启用告警：

```yaml
messageQueue:
  alert:
    enabled: true
    email:
      enabled: true
    sms:
      enabled: true
    webhook:
      enabled: true
```

## 预防措施

### 1. 监控告警
- 消息堆积超过阈值时自动告警
- 连接异常时立即告警
- 监听器失效时告警

### 2. 自动恢复
- 定时检查监听器状态
- 连接异常时自动重连
- 监听器失效时自动重启

### 3. 健康检查
- 集成到Spring Boot Actuator
- 可配置外部监控系统定期检查
- 支持Kubernetes健康检查

## 部署建议

### 1. 分阶段部署
1. 先部署配置优化（JmsConfig）
2. 再部署监控组件
3. 最后启用告警功能

### 2. 监控配置
- 建议先设置较高的告警阈值，观察正常情况下的消息量
- 根据实际情况调整监控参数
- 配置合适的告警接收人

### 3. 测试验证
- 部署后观察监听器是否正常工作
- 测试手动恢复功能
- 验证告警机制是否正常

## 故障排查

### 1. 查看健康状态
```bash
curl http://localhost:8080/api/servicebus/health
```

### 2. 查看监控指标
```bash
curl http://localhost:8080/api/servicebus/metrics
```

### 3. 手动触发恢复
```bash
curl -X POST http://localhost:8080/api/servicebus/recovery/manual
```

### 4. 查看日志
关注以下日志关键字：
- `ServiceBus连接异常`
- `消息队列堆积告警`
- `监听器容器重启`
- `消息处理异常`

## 注意事项

1. **配置调优**：根据实际消息量调整并发数和超时时间
2. **告警频率**：避免告警过于频繁，设置合理的告警间隔
3. **资源监控**：关注CPU和内存使用情况，避免过度并发
4. **网络稳定性**：确保到Azure Service Bus的网络连接稳定

## 后续优化建议

1. **集成APM工具**：如Skywalking、Zipkin等，提供更详细的链路追踪
2. **指标收集**：集成Prometheus等监控系统，收集更多指标
3. **自动扩缩容**：根据消息量自动调整监听器并发数
4. **消息重试策略**：实现更智能的重试机制
