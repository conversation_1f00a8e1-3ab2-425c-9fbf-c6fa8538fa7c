# Service Bus 优化配置模板
# 请将以下配置添加到您的 application.yml 或 nacos 配置中

spring:
  jms:
    listener:
      # 监听器并发配置
      concurrency: "1-5"  # 最小-最大并发数
      # 接收超时时间（毫秒）
      receive-timeout: 30000
      # 恢复间隔时间（毫秒）
      recovery-interval: 5000
      # 空闲超时时间（毫秒）
      idle-timeout: 60000

# Azure Service Bus 配置
azure:
  servicebus:
    connection-string: ${AZURE_SERVICEBUS_CONNECTION_STRING}
    # 客户端ID
    topic-client-id: ${spring.application.name}-${random.uuid}
    # 空闲超时（毫秒）
    idle-timeout: 300000

# 消息队列监控配置
messageQueue:
  monitor:
    # 是否启用监控
    enabled: true
    alert:
      # 消息堆积告警阈值
      threshold: 100
      # 告警间隔时间（毫秒）
      interval: 300000
  
  recovery:
    # 是否启用自动恢复
    enabled: true
    # 最大重试次数
    maxRetries: 3

# 队列名称配置
  atlas:
    syncQueueName: atlas-sync-queue
    syncDeadLetterName: atlas-sync-dlq
  
  cuckoo:
    syncQueueName: cuckoo-sync-queue
    syncDeadLetterName: cuckoo-sync-dlq

# 健康检查配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  health:
    jms:
      enabled: true

# 日志配置
logging:
  level:
    com.navigator.common.serviceBus: DEBUG
    org.springframework.jms: INFO
    org.apache.qpid.jms: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
