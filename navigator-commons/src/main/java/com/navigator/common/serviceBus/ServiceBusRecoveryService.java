package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.config.JmsListenerEndpointRegistry;
import org.springframework.jms.listener.MessageListenerContainer;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.JMSException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service Bus恢复服务
 * 用于自动检测和恢复失效的消息监听器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServiceBusRecoveryService {

    private final ConnectionFactory connectionFactory;
    private final JmsListenerEndpointRegistry jmsListenerEndpointRegistry;
    
    @Value("${messageQueue.recovery.enabled:true}")
    private boolean recoveryEnabled;
    
    @Value("${messageQueue.recovery.maxRetries:3}")
    private int maxRetries;
    
    private final AtomicInteger recoveryAttempts = new AtomicInteger(0);

    /**
     * 定时检查和恢复监听器
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkAndRecoverListeners() {
        if (!recoveryEnabled) {
            return;
        }
        
        try {
            // 检查连接状态
            if (!isConnectionHealthy()) {
                log.warn("Service Bus连接不健康，尝试恢复监听器");
                recoverAllListeners();
            }
            
            // 检查各个监听器状态
            checkListenerContainers();
            
        } catch (Exception e) {
            log.error("监听器恢复检查异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查连接是否健康
     */
    private boolean isConnectionHealthy() {
        try (Connection connection = connectionFactory.createConnection()) {
            connection.start();
            return true;
        } catch (JMSException e) {
            log.warn("Service Bus连接检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查监听器容器状态
     */
    private void checkListenerContainers() {
        for (String containerId : jmsListenerEndpointRegistry.getListenerContainerIds()) {
            MessageListenerContainer container = jmsListenerEndpointRegistry.getListenerContainer(containerId);
            if (container != null) {
                if (!container.isRunning()) {
                    log.warn("监听器容器 {} 未运行，尝试重启", containerId);
                    restartContainer(container, containerId);
                } else {
                    log.debug("监听器容器 {} 运行正常", containerId);
                }
            }
        }
    }
    
    /**
     * 重启指定的监听器容器
     */
    private void restartContainer(MessageListenerContainer container, String containerId) {
        try {
            int attempts = recoveryAttempts.incrementAndGet();
            if (attempts > maxRetries) {
                log.error("监听器容器 {} 重启次数超过最大限制 {}", containerId, maxRetries);
                return;
            }
            
            log.info("正在重启监听器容器 {}, 尝试次数: {}", containerId, attempts);
            
            // 停止容器
            container.stop();
            Thread.sleep(2000); // 等待2秒
            
            // 启动容器
            container.start();
            
            if (container.isRunning()) {
                log.info("监听器容器 {} 重启成功", containerId);
                recoveryAttempts.set(0); // 重置重试计数
            } else {
                log.error("监听器容器 {} 重启失败", containerId);
            }
            
        } catch (Exception e) {
            log.error("重启监听器容器 {} 异常: {}", containerId, e.getMessage(), e);
        }
    }
    
    /**
     * 恢复所有监听器
     */
    public void recoverAllListeners() {
        try {
            log.info("开始恢复所有消息监听器");
            
            // 停止所有监听器
            jmsListenerEndpointRegistry.stop();
            Thread.sleep(5000); // 等待5秒
            
            // 重新启动所有监听器
            jmsListenerEndpointRegistry.start();
            
            log.info("所有消息监听器恢复完成");
            
        } catch (Exception e) {
            log.error("恢复所有监听器异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动触发监听器恢复
     */
    public void manualRecover() {
        log.info("手动触发监听器恢复");
        recoverAllListeners();
    }
    
    /**
     * 获取恢复状态信息
     */
    public RecoveryStatus getRecoveryStatus() {
        int runningContainers = 0;
        int totalContainers = 0;
        
        for (String containerId : jmsListenerEndpointRegistry.getListenerContainerIds()) {
            totalContainers++;
            MessageListenerContainer container = jmsListenerEndpointRegistry.getListenerContainer(containerId);
            if (container != null && container.isRunning()) {
                runningContainers++;
            }
        }
        
        return new RecoveryStatus(
            totalContainers,
            runningContainers,
            recoveryAttempts.get(),
            isConnectionHealthy()
        );
    }
    
    /**
     * 恢复状态信息
     */
    public static class RecoveryStatus {
        private final int totalContainers;
        private final int runningContainers;
        private final int recoveryAttempts;
        private final boolean connectionHealthy;
        
        public RecoveryStatus(int totalContainers, int runningContainers, 
                            int recoveryAttempts, boolean connectionHealthy) {
            this.totalContainers = totalContainers;
            this.runningContainers = runningContainers;
            this.recoveryAttempts = recoveryAttempts;
            this.connectionHealthy = connectionHealthy;
        }
        
        // Getters
        public int getTotalContainers() { return totalContainers; }
        public int getRunningContainers() { return runningContainers; }
        public int getRecoveryAttempts() { return recoveryAttempts; }
        public boolean isConnectionHealthy() { return connectionHealthy; }
        public boolean isAllContainersRunning() { return totalContainers == runningContainers; }
        
        @Override
        public String toString() {
            return String.format("RecoveryStatus{total=%d, running=%d, attempts=%d, healthy=%s}", 
                totalContainers, runningContainers, recoveryAttempts, connectionHealthy);
        }
    }
}
