package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.JMSException;
import javax.jms.Queue;
import javax.jms.QueueBrowser;
import javax.jms.Session;
import java.util.Enumeration;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service Bus监控组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceBusMonitor {

    private final ConnectionFactory connectionFactory;
    private final JmsTemplate jmsTemplate;
    
    @Value("${messageQueue.monitor.enabled:true}")
    private boolean monitorEnabled;
    
    @Value("${messageQueue.monitor.alert.threshold:100}")
    private int alertThreshold;
    
    @Value("${messageQueue.monitor.alert.interval:300000}")
    private long alertInterval;
    
    // 监控指标
    private final AtomicInteger messageCount = new AtomicInteger(0);
    private final AtomicLong lastAlertTime = new AtomicLong(0);
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);

    /**
     * 定时监控消息队列状态
     */
    @Scheduled(fixedDelay = 30000) // 每30秒检查一次
    public void monitorQueues() {
        if (!monitorEnabled) {
            return;
        }
        
        try {
            // 检查主要队列
            checkQueue("${messageQueue.atlas.syncQueueName}");
            checkQueue("${messageQueue.cuckoo.syncQueueName}");
            
            // 重置连续失败计数
            consecutiveFailures.set(0);
            
        } catch (Exception e) {
            int failures = consecutiveFailures.incrementAndGet();
            log.error("消息队列监控异常，连续失败次数: {}, 异常信息: {}", failures, e.getMessage(), e);
            
            // 连续失败3次后发送告警
            if (failures >= 3) {
                sendAlert("消息队列监控异常", 
                    String.format("连续失败%d次，最新异常: %s", failures, e.getMessage()));
            }
        }
    }
    
    /**
     * 检查指定队列的消息数量
     */
    private void checkQueue(String queueName) {
        try (Connection connection = connectionFactory.createConnection();
             Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
            
            connection.start();
            Queue queue = session.createQueue(queueName);
            QueueBrowser browser = session.createBrowser(queue);
            
            // 统计消息数量
            Enumeration<?> messages = browser.getEnumeration();
            int count = 0;
            while (messages.hasMoreElements()) {
                messages.nextElement();
                count++;
            }
            
            messageCount.set(count);
            
            log.debug("队列 {} 当前消息数量: {}", queueName, count);
            
            // 检查是否需要告警
            if (count >= alertThreshold) {
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastAlertTime.get() > alertInterval) {
                    sendAlert("消息队列堆积告警", 
                        String.format("队列 %s 消息堆积数量: %d，超过阈值: %d", queueName, count, alertThreshold));
                    lastAlertTime.set(currentTime);
                }
            }
            
            browser.close();
            
        } catch (JMSException e) {
            log.error("检查队列 {} 失败: {}", queueName, e.getMessage(), e);
            throw new RuntimeException("队列检查失败", e);
        }
    }
    
    /**
     * 发送告警通知
     */
    private void sendAlert(String title, String message) {
        try {
            log.warn("=== 消息队列告警 === 标题: {}, 内容: {}", title, message);
            
            // TODO: 这里可以集成具体的告警通知方式
            // 1. 邮件通知
            // 2. 短信通知  
            // 3. 钉钉/企业微信通知
            // 4. 监控系统告警
            
            // 示例：调用告警服务
            // alertService.sendAlert(title, message);
            
        } catch (Exception e) {
            log.error("发送告警失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取当前监控指标
     */
    public MonitorMetrics getMetrics() {
        return MonitorMetrics.builder()
                .messageCount(messageCount.get())
                .consecutiveFailures(consecutiveFailures.get())
                .lastAlertTime(lastAlertTime.get())
                .alertThreshold(alertThreshold)
                .build();
    }
    
    /**
     * 监控指标数据类
     */
    public static class MonitorMetrics {
        private final int messageCount;
        private final int consecutiveFailures;
        private final long lastAlertTime;
        private final int alertThreshold;
        
        private MonitorMetrics(Builder builder) {
            this.messageCount = builder.messageCount;
            this.consecutiveFailures = builder.consecutiveFailures;
            this.lastAlertTime = builder.lastAlertTime;
            this.alertThreshold = builder.alertThreshold;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private int messageCount;
            private int consecutiveFailures;
            private long lastAlertTime;
            private int alertThreshold;
            
            public Builder messageCount(int messageCount) {
                this.messageCount = messageCount;
                return this;
            }
            
            public Builder consecutiveFailures(int consecutiveFailures) {
                this.consecutiveFailures = consecutiveFailures;
                return this;
            }
            
            public Builder lastAlertTime(long lastAlertTime) {
                this.lastAlertTime = lastAlertTime;
                return this;
            }
            
            public Builder alertThreshold(int alertThreshold) {
                this.alertThreshold = alertThreshold;
                return this;
            }
            
            public MonitorMetrics build() {
                return new MonitorMetrics(this);
            }
        }
        
        // Getters
        public int getMessageCount() { return messageCount; }
        public int getConsecutiveFailures() { return consecutiveFailures; }
        public long getLastAlertTime() { return lastAlertTime; }
        public int getAlertThreshold() { return alertThreshold; }
    }
}
