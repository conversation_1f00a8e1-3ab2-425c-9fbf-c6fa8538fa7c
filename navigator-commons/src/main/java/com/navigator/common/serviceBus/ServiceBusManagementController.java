package com.navigator.common.serviceBus;

import com.navigator.common.dto.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Service Bus管理控制器
 * 提供监控和管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/servicebus")
@RequiredArgsConstructor
public class ServiceBusManagementController {

    private final ServiceBusHealthIndicator healthIndicator;
    private final ServiceBusMonitor monitor;
    private final ServiceBusRecoveryService recoveryService;

    /**
     * 获取Service Bus健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> getHealth() {
        try {
            Health health = healthIndicator.health();
            Map<String, Object> result = new HashMap<>();
            result.put("status", health.getStatus().getCode());
            result.put("details", health.getDetails());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取健康状态失败: {}", e.getMessage(), e);
            return Result.failure("获取健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控指标
     */
    @GetMapping("/metrics")
    public Result<Map<String, Object>> getMetrics() {
        try {
            ServiceBusMonitor.MonitorMetrics metrics = monitor.getMetrics();
            ServiceBusRecoveryService.RecoveryStatus recoveryStatus = recoveryService.getRecoveryStatus();
            
            Map<String, Object> result = new HashMap<>();
            result.put("messageCount", metrics.getMessageCount());
            result.put("consecutiveFailures", metrics.getConsecutiveFailures());
            result.put("lastAlertTime", metrics.getLastAlertTime());
            result.put("alertThreshold", metrics.getAlertThreshold());
            result.put("totalContainers", recoveryStatus.getTotalContainers());
            result.put("runningContainers", recoveryStatus.getRunningContainers());
            result.put("recoveryAttempts", recoveryStatus.getRecoveryAttempts());
            result.put("connectionHealthy", recoveryStatus.isConnectionHealthy());
            result.put("allContainersRunning", recoveryStatus.isAllContainersRunning());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取监控指标失败: {}", e.getMessage(), e);
            return Result.failure("获取监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取恢复状态
     */
    @GetMapping("/recovery/status")
    public Result<ServiceBusRecoveryService.RecoveryStatus> getRecoveryStatus() {
        try {
            ServiceBusRecoveryService.RecoveryStatus status = recoveryService.getRecoveryStatus();
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取恢复状态失败: {}", e.getMessage(), e);
            return Result.failure("获取恢复状态失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发监听器恢复
     */
    @PostMapping("/recovery/manual")
    public Result<String> manualRecover() {
        try {
            log.info("收到手动恢复请求");
            recoveryService.manualRecover();
            return Result.success("监听器恢复操作已触发");
        } catch (Exception e) {
            log.error("手动恢复失败: {}", e.getMessage(), e);
            return Result.failure("手动恢复失败: " + e.getMessage());
        }
    }

    /**
     * 获取完整的Service Bus状态报告
     */
    @GetMapping("/status/report")
    public Result<Map<String, Object>> getStatusReport() {
        try {
            Map<String, Object> report = new HashMap<>();
            
            // 健康状态
            Health health = healthIndicator.health();
            report.put("health", Map.of(
                "status", health.getStatus().getCode(),
                "details", health.getDetails()
            ));
            
            // 监控指标
            ServiceBusMonitor.MonitorMetrics metrics = monitor.getMetrics();
            report.put("metrics", Map.of(
                "messageCount", metrics.getMessageCount(),
                "consecutiveFailures", metrics.getConsecutiveFailures(),
                "lastAlertTime", metrics.getLastAlertTime(),
                "alertThreshold", metrics.getAlertThreshold()
            ));
            
            // 恢复状态
            ServiceBusRecoveryService.RecoveryStatus recoveryStatus = recoveryService.getRecoveryStatus();
            report.put("recovery", Map.of(
                "totalContainers", recoveryStatus.getTotalContainers(),
                "runningContainers", recoveryStatus.getRunningContainers(),
                "recoveryAttempts", recoveryStatus.getRecoveryAttempts(),
                "connectionHealthy", recoveryStatus.isConnectionHealthy(),
                "allContainersRunning", recoveryStatus.isAllContainersRunning()
            ));
            
            // 整体状态评估
            boolean overallHealthy = health.getStatus().getCode().equals("UP") 
                && recoveryStatus.isConnectionHealthy() 
                && recoveryStatus.isAllContainersRunning()
                && metrics.getConsecutiveFailures() == 0;
                
            report.put("overall", Map.of(
                "healthy", overallHealthy,
                "timestamp", System.currentTimeMillis()
            ));
            
            return Result.success(report);
        } catch (Exception e) {
            log.error("获取状态报告失败: {}", e.getMessage(), e);
            return Result.failure("获取状态报告失败: " + e.getMessage());
        }
    }
}
