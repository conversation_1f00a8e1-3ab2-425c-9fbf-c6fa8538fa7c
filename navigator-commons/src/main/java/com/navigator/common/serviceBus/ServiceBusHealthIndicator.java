package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.JMSException;

/**
 * Service Bus健康检查指示器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceBusHealthIndicator implements HealthIndicator {

    private final ConnectionFactory connectionFactory;
    private final JmsTemplate jmsTemplate;

    @Override
    public Health health() {
        try {
            // 检查连接是否正常
            Connection connection = connectionFactory.createConnection();
            connection.start();
            
            // 检查连接状态
            if (connection != null) {
                connection.close();
                return Health.up()
                        .withDetail("status", "Service Bus连接正常")
                        .withDetail("connectionFactory", connectionFactory.getClass().getSimpleName())
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", "Service Bus连接失败")
                        .withDetail("error", "无法创建连接")
                        .build();
            }
        } catch (JMSException e) {
            log.error("Service Bus健康检查失败: {}", e.getMessage(), e);
            return Health.down()
                    .withDetail("status", "Service Bus连接异常")
                    .withDetail("error", e.getMessage())
                    .withException(e)
                    .build();
        } catch (Exception e) {
            log.error("Service Bus健康检查异常: {}", e.getMessage(), e);
            return Health.down()
                    .withDetail("status", "Service Bus检查异常")
                    .withDetail("error", e.getMessage())
                    .withException(e)
                    .build();
        }
    }
}
