package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

/**
 * Service Bus告警服务
 * 负责发送各种告警通知
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServiceBusAlertService {

    @Value("${messageQueue.alert.enabled:true}")
    private boolean alertEnabled;
    
    @Value("${messageQueue.alert.email.enabled:false}")
    private boolean emailAlertEnabled;
    
    @Value("${messageQueue.alert.sms.enabled:false}")
    private boolean smsAlertEnabled;
    
    @Value("${messageQueue.alert.webhook.enabled:false}")
    private boolean webhookAlertEnabled;

    /**
     * 发送消息堆积告警
     */
    public void sendMessageBacklogAlert(String queueName, int messageCount, int threshold) {
        if (!alertEnabled) {
            return;
        }
        
        String title = "消息队列堆积告警";
        String content = String.format(
            "队列名称: %s\n" +
            "当前消息数量: %d\n" +
            "告警阈值: %d\n" +
            "告警时间: %s\n" +
            "建议: 请检查消息消费者状态，必要时重启服务",
            queueName, messageCount, threshold, getCurrentTime()
        );
        
        sendAlert(title, content, AlertLevel.HIGH);
    }
    
    /**
     * 发送连接异常告警
     */
    public void sendConnectionErrorAlert(String error) {
        if (!alertEnabled) {
            return;
        }
        
        String title = "Service Bus连接异常";
        String content = String.format(
            "连接异常详情: %s\n" +
            "告警时间: %s\n" +
            "建议: 请检查网络连接和Service Bus服务状态",
            error, getCurrentTime()
        );
        
        sendAlert(title, content, AlertLevel.CRITICAL);
    }
    
    /**
     * 发送监听器异常告警
     */
    public void sendListenerErrorAlert(String listenerName, String error) {
        if (!alertEnabled) {
            return;
        }
        
        String title = "消息监听器异常";
        String content = String.format(
            "监听器名称: %s\n" +
            "异常详情: %s\n" +
            "告警时间: %s\n" +
            "建议: 请检查监听器状态，必要时手动恢复",
            listenerName, error, getCurrentTime()
        );
        
        sendAlert(title, content, AlertLevel.HIGH);
    }
    
    /**
     * 发送恢复成功通知
     */
    public void sendRecoverySuccessNotification(String component) {
        if (!alertEnabled) {
            return;
        }
        
        String title = "Service Bus恢复成功";
        String content = String.format(
            "恢复组件: %s\n" +
            "恢复时间: %s\n" +
            "状态: 正常运行",
            component, getCurrentTime()
        );
        
        sendAlert(title, content, AlertLevel.INFO);
    }
    
    /**
     * 统一告警发送方法
     */
    private void sendAlert(String title, String content, AlertLevel level) {
        // 异步发送告警，避免阻塞主流程
        CompletableFuture.runAsync(() -> {
            try {
                log.warn("=== Service Bus告警 === 级别: {}, 标题: {}, 内容: {}", level, title, content);
                
                // 根据配置发送不同类型的告警
                if (emailAlertEnabled) {
                    sendEmailAlert(title, content, level);
                }
                
                if (smsAlertEnabled && (level == AlertLevel.CRITICAL || level == AlertLevel.HIGH)) {
                    sendSmsAlert(title, content, level);
                }
                
                if (webhookAlertEnabled) {
                    sendWebhookAlert(title, content, level);
                }
                
            } catch (Exception e) {
                log.error("发送告警失败: {}", e.getMessage(), e);
            }
        });
    }
    
    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(String title, String content, AlertLevel level) {
        try {
            // TODO: 集成邮件服务
            // emailService.sendAlert(title, content, level);
            log.info("邮件告警已发送: {}", title);
        } catch (Exception e) {
            log.error("发送邮件告警失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送短信告警
     */
    private void sendSmsAlert(String title, String content, AlertLevel level) {
        try {
            // TODO: 集成短信服务
            // smsService.sendAlert(title, content, level);
            log.info("短信告警已发送: {}", title);
        } catch (Exception e) {
            log.error("发送短信告警失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送Webhook告警
     */
    private void sendWebhookAlert(String title, String content, AlertLevel level) {
        try {
            // TODO: 集成Webhook服务（钉钉、企业微信等）
            // webhookService.sendAlert(title, content, level);
            log.info("Webhook告警已发送: {}", title);
        } catch (Exception e) {
            log.error("发送Webhook告警失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取当前时间字符串
     */
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO("信息"),
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        CRITICAL("严重");
        
        private final String description;
        
        AlertLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
