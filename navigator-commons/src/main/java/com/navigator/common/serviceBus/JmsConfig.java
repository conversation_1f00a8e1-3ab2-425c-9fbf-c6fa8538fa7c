package com.navigator.common.serviceBus;

import com.azure.spring.autoconfigure.jms.AzureServiceBusJMSProperties;
import com.azure.spring.autoconfigure.jms.ConnectionStringResolver;
import com.azure.spring.autoconfigure.jms.ServiceBusKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.qpid.jms.JmsConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.config.JmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.util.ErrorHandler;

import javax.jms.ConnectionFactory;
import javax.jms.Session;

@Slf4j
@EnableJms
@Configuration
public class JmsConfig {

    @Value("${spring.jms.listener.concurrency:1-5}")
    private String concurrency;

    @Value("${spring.jms.listener.receive-timeout:30000}")
    private Long receiveTimeout;

    @Value("${spring.jms.listener.recovery-interval:5000}")
    private Long recoveryInterval;


    @Bean
    public ConnectionFactory jmsConnectionFactory(AzureServiceBusJMSProperties busJMSProperties) {
        final String connectionString = busJMSProperties.getConnectionString();
        final String clientId = busJMSProperties.getTopicClientId();
        final int configIdleTimeout = busJMSProperties.getIdleTimeout();

        final ServiceBusKey serviceBusKey = ConnectionStringResolver.getServiceBusKey(connectionString);

        // 增强连接配置，添加重连和心跳机制
        final String remoteUri = String.format("amqps://%s?amqp.idleTimeout=%d&amqp.traceFrames=true&jms.connectTimeout=30000&jms.sendTimeout=30000&jms.requestTimeout=30000&amqp.maxFrameSize=1048576",
                serviceBusKey.getHost(), configIdleTimeout);

        final JmsConnectionFactory jmsConnectionFactory =
                new JmsConnectionFactory(
                        serviceBusKey.getSharedAccessKeyName(),
                        serviceBusKey.getSharedAccessKey(),
                        remoteUri
                );
        jmsConnectionFactory.setClientID(clientId);

        CachingConnectionFactory cachingConnectionFactory =
                new CachingConnectionFactory(jmsConnectionFactory);

        // 优化连接缓存配置
        cachingConnectionFactory.setCacheProducers(true);   // 提高发送性能，默认就为true
        cachingConnectionFactory.setCacheConsumers(false);  // 提高消费可靠性，避免状态异常
        cachingConnectionFactory.setSessionCacheSize(10);
        cachingConnectionFactory.setReconnectOnException(true);

        return cachingConnectionFactory;
    }

    @Bean
    public JmsListenerContainerFactory<?> jmsListenerContainerFactory(ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);

        // 消息手动确认
        factory.setSessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE);

        // 是否使用事务
        factory.setSessionTransacted(true);

        // 设置并发配置
        factory.setConcurrency(concurrency);

        // 设置接收超时
        factory.setReceiveTimeout(receiveTimeout);

        // 设置恢复间隔
        factory.setRecoveryInterval(recoveryInterval);


        // 启用自动启动
        factory.setAutoStartup(true);

        // 设置缓存级别
        factory.setCacheLevel(DefaultMessageListenerContainer.CACHE_CONSUMER);

        // 增强错误处理机制
        factory.setErrorHandler(new ServiceBusErrorHandler());

        return factory;
    }

    /**
     * 自定义错误处理器
     */
    public static class ServiceBusErrorHandler implements ErrorHandler {
        private static ServiceBusAlertService alertService;

        // 通过静态方法设置告警服务，因为ErrorHandler是静态内部类
        public static void setAlertService(ServiceBusAlertService service) {
            alertService = service;
        }

        @Override
        public void handleError(Throwable t) {
            log.error("ServiceBus消息处理异常，异常信息: {}", t.getMessage(), t);

            // 发送告警通知
            try {
                if (alertService != null) {
                    alertService.sendListenerErrorAlert("JmsListener", t.getMessage());
                } else {
                    log.warn("告警服务未初始化，无法发送告警: {}", t.getMessage());
                }
            } catch (Exception e) {
                log.error("发送告警失败: {}", e.getMessage(), e);
            }
        }
    }

}
