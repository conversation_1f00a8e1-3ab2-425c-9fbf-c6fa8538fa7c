package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

/**
 * Service Bus配置类
 * 负责初始化各种服务
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ServiceBusConfiguration implements CommandLineRunner {

    private final ServiceBusAlertService alertService;

    @Override
    public void run(String... args) throws Exception {
        // 初始化错误处理器的告警服务
        JmsConfig.ServiceBusErrorHandler.setAlertService(alertService);
        log.info("Service Bus配置初始化完成");
    }
}
