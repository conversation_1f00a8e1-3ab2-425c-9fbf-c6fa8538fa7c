package com.navigator.cuckoo.listener;

import com.google.gson.Gson;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.service.IAtlasSyncService;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Cuckoo 消息队列监听
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/25
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class CuckooMessageQueueListener {

    @Value("${messageQueue.cuckoo.syncQueueName}")
    private String syncQueueName;
    @Value("${messageQueue.cuckoo.syncDeadLetterName}")
    private String syncDeadLetterName;

    // service
    private final IAtlasSyncService syncService;

    /**
     * Trade → Cuckoo 消息监听
     */
    @JmsListener(destination = "${messageQueue.cuckoo.syncQueueName}",
                containerFactory = "jmsListenerContainerFactory",
                concurrency = "1-3")
    public void receiveQueueMessage(String message, Session session, javax.jms.Message jmsMessage) {
        log.info("[{}]Queue Message received: {}", syncQueueName, message);

        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            // 验证消息格式
            if (message == null || message.trim().isEmpty()) {
                log.error("接收到空消息，直接确认并丢弃");
                jmsMessage.acknowledge();
                return;
            }

            AtlasSyncRequestDTO syncRequestDTO = new Gson().fromJson(message, AtlasSyncRequestDTO.class);

            // 验证必要字段
            if (syncRequestDTO == null) {
                log.error("消息解析失败，内容: {}", message);
                jmsMessage.acknowledge(); // 确认消息，避免重复处理
                return;
            }

            syncService.syncContractRequest(syncRequestDTO);

            // 处理成功，手动确认消息
            jmsMessage.acknowledge();
            success = true;

            log.info("[{}]消息处理成功，耗时: {}ms", syncQueueName, System.currentTimeMillis() - startTime);

        } catch (RetryableException e) {
            log.error("method: receiveQueueMessage, send Cuckoo requestData error,retry......: {}", e.getMessage());

            // 不确认消息，让其重新投递
            throw new BusinessException(ResultCodeEnum.SYNC_CUCKOO_CONTRACT_EXCEPTION, e.getMessage());
        } catch (Exception e) {
            log.error("method: receiveQueueMessage, send Cuckoo requestData error: {}", e.getMessage(), e);

            try {
                // 对于非重试异常，确认消息避免重复处理
                jmsMessage.acknowledge();
            } catch (Exception ackException) {
                log.error("消息确认失败: {}", ackException.getMessage(), ackException);
            }
        } finally {
            // 记录处理结果
            log.info("[{}]消息处理完成，成功: {}, 总耗时: {}ms", syncQueueName, success, System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 同步Cuckoo死信队列监听
     *
     * @param message
     */
    @JmsListener(destination = "${messageQueue.cuckoo.syncDeadLetterName}", containerFactory = "jmsListenerContainerFactory")
    public void receiveDLQueueMessage(String message) {
        log.info("[{}]DLQ Message received: {}", syncDeadLetterName, message);
    }
}
