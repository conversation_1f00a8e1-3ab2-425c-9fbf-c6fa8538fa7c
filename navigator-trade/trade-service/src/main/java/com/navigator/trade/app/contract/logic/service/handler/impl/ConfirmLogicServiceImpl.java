package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.ConfirmLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.pojo.dto.contract.ContractConfirmResultDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.SyncSwitchEnum;
import com.navigator.trade.service.ITtAddService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 确认合同变更
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/16
 */
@Slf4j
@Service
public class ConfirmLogicServiceImpl implements ConfirmLogicService {

    @Autowired
    protected ITtAddService ttAddService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    ContractSignQueryLogicService contractSignQueryLogicService;
    /**
     * 合同Logic 公共的处理方法
     */
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    @Autowired
    private CommonLogicService commonLogicService;
    @Autowired
    private IContractSignQueryService contractSignQueryService;
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private TTLogicService ttLogicService;
    @Autowired
    private ContractSignLogicService contractSignLogicService;
    @Autowired
    private AtlasContractFacade atlasContractFacade;
    @Autowired
    private ITtPriceService ttPriceService;

    // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-29 start
    @Autowired
    @Qualifier("contractSyncExecutor")
    private ThreadPoolTaskExecutor contractSyncExecutor;
    // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-29 end

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractConfirmResultDTO confirmContractModify(ContractModifyDTO contractModifyDTO) {

        ContractConfirmResultDTO contractConfirmResultDTO = new ContractConfirmResultDTO();
        // 获取合同信息
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractModifyDTO.getContractId());
        contractConfirmResultDTO
                .setContractAction(ContractActionEnum.getByType(contractModifyDTO.getContractSource()).name())
                .setOriginalContractEntity(contractEntity);

        // 生效中的合同不同步
        if (contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())
                && !contractModifyDTO.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getTradeType())) {
            return contractConfirmResultDTO.setNotSyncReason("生效中的合同不同步");
        }

        switch (ContractActionEnum.getByType(contractModifyDTO.getContractSource())) {
            case ALLOCATE:
            case ASSIGN:
            case NEW:
                // 合同进入生效中  | 仓单转让 | 采购仓单生效 | 现货生效
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 如果是采购仓单生效那么需要通知仓单创建仓单数据
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode()) && ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                    // TODO 创建仓单属性为 仓单 仓单信息
                    TTAddEntity ttAddEntity = ttAddService.getTTAddEntityByTTId(contractModifyDTO.getTtId());
                    WarrantEntity warrantEntity = commonLogicService.createPurchaseWarrant(contractEntity, ttAddEntity.getWarrantCategory(), ttAddEntity.getDeliveryMarginAmount());
                    // 更新TT/TT_ADD的仓单信息
                    ttLogicService.updateTTAddWarrantId(contractModifyDTO.getTtId(), warrantEntity.getId(), warrantEntity.getCode());
                    // 更新协议
                    contractSignLogicService.updateSignWarrantId(contractModifyDTO.getSignId(), warrantEntity.getId(), warrantEntity.getCode());
                }
                // 更新仓单状态
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode()) && ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
                    commonLogicService.updateWarranStatus(contractEntity.getWarrantCode());
                }
                // 记录版本信息      getTradeType   备份类型  getCode    关联编码
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步到相关系统 atlas|Lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                break;
            case REVISE:
                // 合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步到相关系统 atlas|Lkg
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.BOTH);
                    }
                });
                break;
            case REVISE_CUSTOMER:
                // 新合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步合同信息到到lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                // 原合同进入生效中
                ContractEntity reviseParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                contractConfirmResultDTO.setParentContractEntity(reviseParentContract);
                if (null != reviseParentContract && reviseParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    // 合同进入生效，备份合同信息
                    contractDomainService.updateContractById(reviseParentContract.setStatus(ContractStatusEnum.EFFECTIVE.getValue()),
                            String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                    // 根据当前tt获取父合同更新的tt
                    TradeTicketEntity ticketEntity = ttQueryLogicService.getByTtId(contractModifyDTO.getTtId());
                    if (null != ticketEntity) {
                        TradeTicketEntity parentTradeTicket = ttQueryLogicService.getIncludeDeletedByGroupId(ticketEntity.getGroupId(), ticketEntity.getId());
                        if (null != parentTradeTicket) {
                            // 同步更新父合同的信息 Lkg
                            commonLogicService.syncContractInfo(reviseParentContract, parentTradeTicket.getId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
                        }
                    }
                }
                break;
            case SPLIT:
            case SPLIT_CUSTOMER:
                updateBySplitConfirm(contractEntity, contractModifyDTO, contractConfirmResultDTO);
                break;
            case TRANSFER_CONFIRM:
                // 新合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步合同信息到到  atlas|Lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                // 原合同进入生效中
                ContractEntity transferParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                contractConfirmResultDTO.setParentContractEntity(transferParentContract);
                if (null != transferParentContract && transferParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    transferParentContract.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                    // 合同进入生效，备份合同信息
                    contractDomainService.updateContractById(transferParentContract, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                    // 同步更新父合同信息
                    commonLogicService.syncContractInfo(transferParentContract, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
                }
                break;
            case TRANSFER_ALL_CONFIRM:
                // 合同进入生效，备份合同信息
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue()),
                        String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步更新父合同信息
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.BOTH);
                break;
            case REVERSE_PRICE_CONFIRM:
            case REVERSE_PRICE_ALL_CONFIRM:
                // 新合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步合同信息到到lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                // 原合同进入生效中
                ContractEntity reverseParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                contractConfirmResultDTO.setParentContractEntity(reverseParentContract);
                if (null != reverseParentContract) {
                    reverseParentContract.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                    // 记录版本信息
                    contractDomainService.updateContractById(reverseParentContract, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                    // 同步更新父合同信息
                    commonLogicService.syncContractInfo(reverseParentContract, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
                }
                break;
            case WASHOUT:
                int washOutStatus = ContractStatusEnum.EFFECTIVE.getValue();
                // 直接比较数据量
                // BUGFIX：Case-1003226 解约定赔全部、部分逻辑修复 Author: Mr 2025-06-06 Start
                if (BigDecimalUtil.isZero(contractEntity.getContractNum()
                        .subtract(contractEntity.getTotalBuyBackNum())
                        .subtract(contractEntity.getWarrantCancelCount()))) {
                    // BUGFIX：Case-1003226 解约定赔全部、部分逻辑修复 Author: Mr 2025-06-06 End
                    washOutStatus = ContractStatusEnum.COMPLETED.getValue();
                }
                // 合同进入生效，备份合同信息
                contractDomainService.updateContractById(contractEntity.setStatus(washOutStatus),
                        String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());

                // 同步合同信息到ATLAS
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                break;
            case BUYBACK:
                // TODO 需要优化改造 区分仓单回购|现货回购
                // 新合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // 同步合同信息到到lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                // 回购如果是仓单合同【修改仓单持有量】
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
                    commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_BUYBACK.getValue(),
                            contractEntity.getContractNum(), null);
                }
                // 原合同进入生效中
                ContractEntity buyBackParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                contractConfirmResultDTO.setParentContractEntity(buyBackParentContract);
                if (null != buyBackParentContract && buyBackParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    int buyBackContractStatus = ContractStatusEnum.EFFECTIVE.getValue();
                    // 如果是仓单客户进行回购的动作时候需要计算下注销量
                    BigDecimal buyBackNum = buyBackParentContract.getTotalBuyBackNum();
                    if (BuCodeEnum.WT.getValue().equals(buyBackParentContract.getBuCode())) {
                        buyBackNum = buyBackNum.add(buyBackParentContract.getWarrantCancelCount());
                    }
                    if (BigDecimalUtil.isEqual(buyBackParentContract.getContractNum(), buyBackNum)) {
                        buyBackContractStatus = ContractStatusEnum.COMPLETED.getValue();
                    }
                    // 合同进入生效，备份合同信息
                    contractDomainService.updateContractById(buyBackParentContract.setStatus(buyBackContractStatus)
                            , String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());

                    // BUGFIX：Case-1003194 合同回购未提货量显示问题 Author: Mr 2025-05-13 Start
                    Result<AtlasMappingContractEntity> contractExecute = atlasContractFacade.getByNavContractCode(buyBackParentContract.getContractCode());
                    if (null != contractExecute && null != contractExecute.getData()) {
                        AtlasMappingContractEntity atlasMappingContractEntity = contractExecute.getData();
                        atlasMappingContractEntity.setExecutedNum(buyBackParentContract.getTotalBuyBackNum());
                        atlasMappingContractEntity.setUpdatedAt(DateTimeUtil.now());
                        atlasContractFacade.updateAtlasMappingContract(atlasMappingContractEntity);
                    }
                    // BUGFIX：Case-1003194 合同回购未提货量显示问题 Author: Mr 2025-05-13 End

                    //  同步更新父合同信息
                    commonLogicService.syncContractInfo(buyBackParentContract, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
                }
                break;
            case INVALID:
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()).setTotalPriceNum(BigDecimal.ZERO));
                // 如果是仓单合同需要更新仓单的数量【作废】
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
                    commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_INVALID.getValue(), contractEntity.getContractNum(), null);
                }
                // TODO 【仓单合同作废】推送到Atlas,如果是基差合同需要把定价也取消掉
                commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.CLOSE.getSyncType(), SyncSwitchEnum.ATLAS);
                // TODO 存在定价的话进行取消定价 取消定价待完善【需要进行校验】
                log.info("========作废合同ID==========:" + contractEntity.getId());
                List<TTPriceEntity> priceEntities = ttLogicService.withdrawPrice(contractEntity.getId());
                // 存在定价的话进行取消定价
                log.info("========合同的订单信息==========:" + priceEntities.size());
                if (ObjectUtil.isNotEmpty(priceEntities) && priceEntities.size() > 0) {
                    commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), priceEntities.get(0).getTtId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
                }
                break;
            case CLOSED:
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.CLOSED.getValue()));
                // 同步更新信息
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.DELETE.getSyncType(), SyncSwitchEnum.BOTH);
                break;
            case STRUCTURE_PRICE_CONFIRM:
                // 合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                break;
            case WARRANT_WRITEOFF:
                // 合同进入生效中
                contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 记录版本信息
                contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                // TODO 推送到Atlas 【提货合同/采购合同】【一起同步的问题,一个一个同步】
                // 同步合同信息到到lkg
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.ATLAS);
                // TODO 父合同的同步的操作【父合同同步只有注销二才进行同步】
                ContractEntity parentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                if (null != parentContract && parentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())
                        && ContractActionEnum.WRITE_OFF_B.getActionValue() == contractModifyDTO.getTtTradeType()) {
                    // 合同进入生效，备份合同信息
                    parentContract.setCreateBatch(ContractTradeTypeEnum.UNKNOWN.getDesc());
                    contractDomainService.updateContractById(parentContract.setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                            , String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                    //  同步更新父合同信息
                    commonLogicService.syncContractInfo(parentContract, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                }
                break;
            default:
                break;
        }

        return contractConfirmResultDTO;
    }

    /**
     * 拆分确认
     *
     * @param contractEntity
     * @param contractModifyDTO
     * @param contractConfirmResultDTO
     */
    private void updateBySplitConfirm(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, ContractConfirmResultDTO contractConfirmResultDTO) {
        // 判断合同操作来源
        ContractEntity splitCustomerContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());

        contractConfirmResultDTO.setParentContractEntity(splitCustomerContract);

        // 主体拆分的子合同确认合规
        if (null != splitCustomerContract
                && contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {

            // 新合同进入生效中
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
            // 记录版本信息
            contractDomainService.updateContractById(contractEntity, String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
            // 同步合同信息到到lkg
            // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-4 start
            syncSplitContractInfo(splitCustomerContract, contractEntity, contractModifyDTO);
            // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-4 end
            // 合同进入生效，备份合同信息
            int contractStatus = getContractStatus(splitCustomerContract);
            if (contractStatus != ContractStatusEnum.SPLITTING.getValue()) {
                contractDomainService.updateContractById(splitCustomerContract.setStatus(contractStatus),
                        String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
            }
            // 父合同的定价更新
            if (Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())
                    .contains(splitCustomerContract.getContractType())
                    && contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {

                // 同步更新父合同定价信息
                commonLogicService.syncContractPriceUpdateInfo(contractModifyDTO.getTtId(), contractEntity);
            }
        } else {
            // 拆分的父合同确认合规
            if (!contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                // 合同进入生效，备份合同信息
                int contractStatus = getContractStatus(contractEntity);
                if (contractStatus != ContractStatusEnum.SPLITTING.getValue()) {
                    // 记录版本信息
                    contractDomainService.updateContractById(contractEntity.setStatus(contractStatus), String.valueOf(contractModifyDTO.getTtTradeType()), contractModifyDTO.getTtCode());
                }
                // 同步更新父合同信息
                commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
            }
        }
    }

    // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-4 start

    /**
     * 同步拆分合同信息
     *
     * @param parentContract    父合同
     * @param contractEntity    拆分的子合同
     * @param contractModifyDTO 合同变更信息
     */
    private void syncSplitContractInfo(ContractEntity parentContract, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {

        // 基差暂定价拆分基差暂定价流程（带定价单）
        boolean isBasisTempPricing = Objects.equals(contractEntity.getContractType(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue());

        // 根据ttId查询定价单信息
        List<TTPriceEntity> priceEntityList = ttPriceService.getTtPriceListByTtId(contractModifyDTO.getTtId());

        if (isBasisTempPricing && CollectionUtil.isNotEmpty(priceEntityList)) {
            // 1. 同步子合同信息
            commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.ATLAS);

            // 2. 使用公共线程池异步重试同步后续信息
            CompletableFuture.runAsync(() -> {
                final int maxRetries = 5;
                final int retryIntervalMs = 10000;

                int currentRetry = 0;
                boolean success = false;

                log.info("【拆分合同】开始轮询获取合同映射信息，合同编号: {}", contractEntity.getContractCode());

                while (currentRetry < maxRetries && !success) {
                    try {
                        // 查询合同映射信息
                        Result<AtlasMappingContractEntity> result = atlasContractFacade.getByNavContractCode(contractEntity.getContractCode());

                        // 检查查询结果
                        if (result != null && result.isSuccess() && result.getData() != null) {
                            log.info("成功获取到合同映射信息，合同编号: {}, 重试次数: {}", contractEntity.getContractCode(), currentRetry);
                            success = true;

                            // 2. 原合同定价单-全撤
                            commonLogicService.syncTTPriceInfo(parentContract.getSiteCode(), contractModifyDTO.getTtId(), LkgInterfaceActionEnum.PRICE.getSyncType());

                            // 3. 同步新子合同定价单
                            commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), contractModifyDTO.getTtId(), LkgInterfaceActionEnum.PRICE.getSyncType());

                            // 4. 同步父合同首个定价单
                            priceEntityList.stream()
                                    .findFirst()
                                    .map(TTPriceEntity::getSourceId)
                                    .map(ttPriceService::getById)
                                    .ifPresent(priceEntity ->
                                            commonLogicService.syncTTPriceInfo(parentContract.getSiteCode(), priceEntity.getTtId(), LkgInterfaceActionEnum.PRICE.getSyncType()));
                        } else {
                            currentRetry++;
                            log.warn("【拆分合同】未获取到映射信息，合同编号: {}, 当前尝试: {}/{}", contractEntity.getContractCode(), currentRetry, maxRetries);
                            Thread.sleep(retryIntervalMs);
                        }
                    } catch (Exception e) {
                        currentRetry++;
                        log.warn("【拆分合同】获取合同映射信息异常，合同编号: {}, 当前尝试: {}/{}, 异常: {}", contractEntity.getContractCode(), currentRetry, maxRetries, e.getMessage(), e);
                        try {
                            Thread.sleep(retryIntervalMs);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }

                if (!success) {
                    log.error("【拆分合同】达到最大重试次数仍未获取到映射信息，合同编号: {}", contractEntity.getContractCode());
                    // TODO: 可在此处加入告警通知或补偿任务
                }
            }, contractSyncExecutor);

        } else {
            // 普通同步逻辑
            commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
        }
    }
    // 1002083 基差暂定价拆分定价单 changed by Mr at 2025-7-4 end

    /**
     * 获取拆分中的父合同的状态
     *
     * @param contractEntity
     * @return
     */
    private Integer getContractStatus(ContractEntity contractEntity) {
        int status = ContractStatusEnum.SPLITTING.getValue();

        List<ContractEntity> sonContractList = contractQueryDomainService.getContractByPid(contractEntity.getId());
        List<Integer> ids = new ArrayList<>();

        // 子合同id
        sonContractList.forEach(entity -> ids.add(entity.getId()));

        // 父合同协议
        List<ContractSignEntity> contractSignEntityList = contractSignQueryLogicService.queryIncompleteByContractId(
                Collections.singletonList(contractEntity.getId()),
                Collections.singletonList(TTTypeEnum.SPLIT.getType()));

        // 子合同协议
        List<ContractSignEntity> sonContractSignEntityList = contractSignQueryService.querySonContractSplitIncomplete(ids);

        boolean completeFlag = ObjectUtil.isEmpty(contractSignEntityList) && ObjectUtil.isEmpty(sonContractSignEntityList);

        if (BigDecimalUtil.isZero(contractEntity.getContractNum()) && completeFlag) {
            status = ContractStatusEnum.COMPLETED.getValue();
        }

        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getContractNum()) && completeFlag) {
            status = ContractStatusEnum.EFFECTIVE.getValue();
        }

        return status;
    }

}
