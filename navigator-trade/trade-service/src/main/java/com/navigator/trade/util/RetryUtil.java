package com.navigator.trade.util;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 重试工具类
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
public class RetryUtil {
    
    /**
     * 执行带重试的操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param retryIntervalMs 重试间隔时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, int maxRetries, long retryIntervalMs, String operationName) {
        Exception lastException = null;
        
        for (int currentRetry = 0; currentRetry < maxRetries; currentRetry++) {
            try {
                T result = operation.get();
                if (result != null) {
                    if (currentRetry > 0) {
                        log.info("{}成功，尝试次数: {}", operationName, currentRetry + 1);
                    }
                    return result;
                }
                
                log.warn("{}返回null，当前尝试: {}/{}", operationName, currentRetry + 1, maxRetries);
            } catch (Exception e) {
                lastException = e;
                log.warn("{}异常，当前尝试: {}/{}, 异常: {}", operationName, currentRetry + 1, maxRetries, e.getMessage());
            }
            
            // 如果不是最后一次重试，则等待
            if (currentRetry < maxRetries - 1) {
                try {
                    Thread.sleep(retryIntervalMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("{}重试被中断", operationName);
                    break;
                }
            }
        }
        
        log.error("{}达到最大重试次数仍未成功，最后异常: {}", operationName, 
            lastException != null ? lastException.getMessage() : "无异常信息");
        return null;
    }
    
    /**
     * 执行带重试的布尔操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param retryIntervalMs 重试间隔时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作是否成功
     */
    public static boolean executeWithRetryBoolean(Supplier<Boolean> operation, int maxRetries, long retryIntervalMs, String operationName) {
        Exception lastException = null;
        
        for (int currentRetry = 0; currentRetry < maxRetries; currentRetry++) {
            try {
                Boolean result = operation.get();
                if (Boolean.TRUE.equals(result)) {
                    if (currentRetry > 0) {
                        log.info("{}成功，尝试次数: {}", operationName, currentRetry + 1);
                    }
                    return true;
                }
                
                log.warn("{}失败，当前尝试: {}/{}", operationName, currentRetry + 1, maxRetries);
            } catch (Exception e) {
                lastException = e;
                log.warn("{}异常，当前尝试: {}/{}, 异常: {}", operationName, currentRetry + 1, maxRetries, e.getMessage());
            }
            
            // 如果不是最后一次重试，则等待
            if (currentRetry < maxRetries - 1) {
                try {
                    Thread.sleep(retryIntervalMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("{}重试被中断", operationName);
                    break;
                }
            }
        }
        
        log.error("{}达到最大重试次数仍未成功，最后异常: {}", operationName, 
            lastException != null ? lastException.getMessage() : "无异常信息");
        return false;
    }
}
