package com.navigator.trade.service.contract;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.common.dto.Result;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.config.ContractSyncRetryConfig;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.pojo.enums.SyncSwitchEnum;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同同步异步服务
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
public class ContractSyncAsyncService {
    
    @Autowired
    private AtlasContractFacade atlasContractFacade;
    
    @Autowired
    private CommonLogicService commonLogicService;
    
    @Autowired
    private ITtPriceService ttPriceService;
    
    @Autowired
    private ContractSyncRetryConfig retryConfig;
    
    /**
     * 异步处理拆分合同的后续同步逻辑
     */
    @Async
    public void processSplitContractAsync(ContractEntity parentContract, ContractEntity contractEntity, 
                                        ContractModifyDTO contractModifyDTO, List<TTPriceEntity> priceEntityList) {
        try {
            log.info("【拆分合同】开始异步处理后续同步逻辑，合同编号: {}", contractEntity.getContractCode());
            
            // 轮询获取合同映射信息
            boolean success = waitForContractMapping(contractEntity.getContractCode());
            
            if (success) {
                // 执行后续同步操作
                executePostSyncOperations(parentContract, contractEntity, contractModifyDTO, priceEntityList);
                log.info("【拆分合同】异步处理完成，合同编号: {}", contractEntity.getContractCode());
            } else {
                log.error("【拆分合同】达到最大重试次数仍未获取到映射信息，合同编号: {}", contractEntity.getContractCode());
                // TODO: 可在此处加入告警通知或补偿任务
            }
        } catch (Exception e) {
            log.error("【拆分合同】异步处理异常，合同编号: {}, 异常: {}", contractEntity.getContractCode(), e.getMessage(), e);
        }
    }
    
    /**
     * 轮询等待合同映射信息
     */
    private boolean waitForContractMapping(String contractCode) {
        if (!retryConfig.isEnabled()) {
            log.warn("【拆分合同】重试功能已禁用，直接返回失败，合同编号: {}", contractCode);
            return false;
        }
        
        log.info("【拆分合同】开始轮询获取合同映射信息，合同编号: {}", contractCode);
        
        return RetryUtil.executeWithRetryBoolean(
            () -> checkContractMapping(contractCode),
            retryConfig.getMaxRetries(),
            retryConfig.getRetryIntervalMs(),
            String.format("【拆分合同】获取合同映射信息，合同编号: %s", contractCode)
        );
    }
    
    /**
     * 检查合同映射信息
     */
    private boolean checkContractMapping(String contractCode) {
        Result<AtlasMappingContractEntity> result = atlasContractFacade.getByNavContractCode(contractCode);
        return isValidResult(result);
    }
    
    /**
     * 检查结果是否有效
     */
    private boolean isValidResult(Result<AtlasMappingContractEntity> result) {
        return result != null && result.isSuccess() && result.getData() != null;
    }
    
    /**
     * 执行后续同步操作
     */
    private void executePostSyncOperations(ContractEntity parentContract, ContractEntity contractEntity, 
                                         ContractModifyDTO contractModifyDTO, List<TTPriceEntity> priceEntityList) {
        try {
            // 1. 原合同定价单-全撤
            syncParentContractPriceUpdate(parentContract, contractModifyDTO);
            
            // 2. 同步新子合同定价单
            syncChildContractPrice(contractEntity, contractModifyDTO);
            
            // 3. 同步父合同首个定价单
            syncParentContractFirstPrice(parentContract, priceEntityList);
            
            log.info("【拆分合同】后续同步操作完成，合同编号: {}", contractEntity.getContractCode());
        } catch (Exception e) {
            log.error("【拆分合同】执行后续同步操作异常，合同编号: {}, 异常: {}", contractEntity.getContractCode(), e.getMessage(), e);
            throw e; // 重新抛出异常，让调用方知道操作失败
        }
    }
    
    /**
     * 同步父合同定价单更新
     */
    private void syncParentContractPriceUpdate(ContractEntity parentContract, ContractModifyDTO contractModifyDTO) {
        try {
            commonLogicService.syncContractInfo(parentContract, contractModifyDTO.getTtId(), 
                LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
            log.debug("【拆分合同】父合同定价单更新同步完成，合同编号: {}", parentContract.getContractCode());
        } catch (Exception e) {
            log.error("【拆分合同】同步父合同定价单更新异常，合同编号: {}, 异常: {}", 
                parentContract.getContractCode(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 同步子合同定价单
     */
    private void syncChildContractPrice(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        try {
            commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), contractModifyDTO.getTtId(), 
                LkgInterfaceActionEnum.PRICE.getSyncType());
            log.debug("【拆分合同】子合同定价单同步完成，合同编号: {}", contractEntity.getContractCode());
        } catch (Exception e) {
            log.error("【拆分合同】同步子合同定价单异常，合同编号: {}, 异常: {}", 
                contractEntity.getContractCode(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 同步父合同首个定价单
     */
    private void syncParentContractFirstPrice(ContractEntity parentContract, List<TTPriceEntity> priceEntityList) {
        if (CollectionUtil.isEmpty(priceEntityList)) {
            log.warn("【拆分合同】定价单列表为空，跳过父合同首个定价单同步，合同编号: {}", parentContract.getContractCode());
            return;
        }
        
        priceEntityList.stream()
                .findFirst()
                .map(TTPriceEntity::getSourceId)
                .map(ttPriceService::getById)
                .ifPresent(priceEntity -> {
                    try {
                        commonLogicService.syncTTPriceInfo(parentContract.getSiteCode(), priceEntity.getTtId(), 
                            LkgInterfaceActionEnum.PRICE.getSyncType());
                        log.debug("【拆分合同】父合同首个定价单同步完成，合同编号: {}, 定价单ID: {}", 
                            parentContract.getContractCode(), priceEntity.getTtId());
                    } catch (Exception e) {
                        log.error("【拆分合同】同步父合同首个定价单异常，合同编号: {}, 定价单ID: {}, 异常: {}", 
                            parentContract.getContractCode(), priceEntity.getTtId(), e.getMessage(), e);
                        throw new RuntimeException("同步父合同首个定价单失败", e);
                    }
                });
    }
}
