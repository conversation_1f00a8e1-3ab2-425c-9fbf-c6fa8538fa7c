package com.navigator.trade.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 合同同步重试配置
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "contract.sync.retry")
public class ContractSyncRetryConfig {
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 5;
    
    /**
     * 重试间隔时间（毫秒）
     */
    private long retryIntervalMs = 10000L;
    
    /**
     * 超时时间（毫秒）
     */
    private long timeoutMs = 60000L;
    
    /**
     * 是否启用重试
     */
    private boolean enabled = true;
}
