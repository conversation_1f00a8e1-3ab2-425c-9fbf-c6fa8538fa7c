package com.navigator.trade.service.contract;

import com.navigator.common.dto.Result;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.config.ContractSyncRetryConfig;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.service.ITtPriceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ContractSyncAsyncService 测试类
 */
@ExtendWith(MockitoExtension.class)
class ContractSyncAsyncServiceTest {

    @Mock
    private AtlasContractFacade atlasContractFacade;

    @Mock
    private CommonLogicService commonLogicService;

    @Mock
    private ITtPriceService ttPriceService;

    @Mock
    private ContractSyncRetryConfig retryConfig;

    @InjectMocks
    private ContractSyncAsyncService contractSyncAsyncService;

    private ContractEntity parentContract;
    private ContractEntity contractEntity;
    private ContractModifyDTO contractModifyDTO;
    private List<TTPriceEntity> priceEntityList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        parentContract = new ContractEntity();
        parentContract.setContractCode("PARENT001");
        parentContract.setSiteCode("SITE001");

        contractEntity = new ContractEntity();
        contractEntity.setContractCode("CONTRACT001");
        contractEntity.setSiteCode("SITE001");

        contractModifyDTO = new ContractModifyDTO();
        contractModifyDTO.setTtId(12345);

        TTPriceEntity priceEntity = new TTPriceEntity();
        priceEntity.setSourceId(1);
        priceEntity.setTtId(12345);
        priceEntityList = Arrays.asList(priceEntity);

        // 设置默认的重试配置
        when(retryConfig.isEnabled()).thenReturn(true);
        when(retryConfig.getMaxRetries()).thenReturn(3);
        when(retryConfig.getRetryIntervalMs()).thenReturn(1000L);
    }

    @Test
    void testProcessSplitContractAsync_Success() {
        // 模拟成功获取合同映射信息
        Result<AtlasMappingContractEntity> successResult = Result.success(new AtlasMappingContractEntity());
        when(atlasContractFacade.getByNavContractCode(anyString())).thenReturn(successResult);

        // 模拟ttPriceService返回定价实体
        TTPriceEntity mockPriceEntity = new TTPriceEntity();
        mockPriceEntity.setTtId(12345);
        when(ttPriceService.getById(anyInt())).thenReturn(mockPriceEntity);

        // 执行测试
        contractSyncAsyncService.processSplitContractAsync(parentContract, contractEntity, contractModifyDTO, priceEntityList);

        // 由于是异步方法，需要等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证调用
        verify(atlasContractFacade, atLeastOnce()).getByNavContractCode("CONTRACT001");
        verify(commonLogicService, times(1)).syncContractInfo(eq(parentContract), eq(12345), anyInt(), any());
        verify(commonLogicService, times(1)).syncTTPriceInfo(eq("SITE001"), eq(12345), anyInt());
    }

    @Test
    void testProcessSplitContractAsync_RetryDisabled() {
        // 禁用重试
        when(retryConfig.isEnabled()).thenReturn(false);

        // 执行测试
        contractSyncAsyncService.processSplitContractAsync(parentContract, contractEntity, contractModifyDTO, priceEntityList);

        // 等待异步执行完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证不会调用Atlas接口
        verify(atlasContractFacade, never()).getByNavContractCode(anyString());
    }

    @Test
    void testProcessSplitContractAsync_MaxRetriesReached() {
        // 模拟总是返回失败结果
        Result<AtlasMappingContractEntity> failResult = Result.fail("获取失败");
        when(atlasContractFacade.getByNavContractCode(anyString())).thenReturn(failResult);

        // 执行测试
        contractSyncAsyncService.processSplitContractAsync(parentContract, contractEntity, contractModifyDTO, priceEntityList);

        // 等待异步执行完成
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证会重试指定次数
        verify(atlasContractFacade, times(3)).getByNavContractCode("CONTRACT001");
        // 由于获取映射信息失败，不应该执行后续同步操作
        verify(commonLogicService, never()).syncContractInfo(eq(parentContract), anyInt(), anyInt(), any());
    }
}
